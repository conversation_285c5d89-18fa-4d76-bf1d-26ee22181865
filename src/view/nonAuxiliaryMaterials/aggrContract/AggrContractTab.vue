<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="headTab" tab="合同与协议" >
          <aggr-contract-edit  ref="headTab"  :edit-config="editConfig"  @onEditBack="editBack" @editShowBody="editShowBody"></aggr-contract-edit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="attach" tab="归档附件" @onEditBack="editBack" >
          <aggr-contract-attach :head-id="headId"  :operation-status="editConfig.editStatus"></aggr-contract-attach>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="agreement" tab="协议信息" @onEditBack="editBack" >
          <aggr-contract-agreement :head-id="headId" :edit-config="editConfig" @onEditBack="editBack"></aggr-contract-agreement>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="aeo" tab="审批记录" @onEditBack="editBack" >
          <!-- 确保子组件重新挂载 -->
        </a-tab-pane>
        <template #rightExtra>
          <div class="cs-tab-icon" @click="editBack">
            <GlobalIcon type="close-circle" style="color:#000"/>
          </div>
        </template>
      </a-tabs>
    </div>

  </section>
</template>

<script setup>

import {onMounted, reactive, ref, watch} from "vue";
import {editStatus} from "@/view/common/constant";
import AggrContractEdit from "@/view/nonAuxiliaryMaterials/aggrContract/AggrContractnEdit.vue";
import AggrContractAttach from "./components/AggrContractAttach.vue";

defineOptions({
  name:'AggrContractTab'
})

const emit = defineEmits(['onEditBack','editShowBody'])

/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});


/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}

/* 激活Tab key */
const tabName = ref('headTab');

/* 总tab信息 */
const tabs = reactive({
  headTab:true,
  shipFrom:true,
})

/* 表头headId */
const headId = ref('')


/* 是否显示子模块 tab */
const showBody = ref(false)

const editShowBody = (val, editStatus, headid) =>{
  if(val){
    showBody.value = val
    headId.value =headid
    props.editConfig.editStatus = editStatus
  }
}

/* 返回tab界面 */
const editBack = (val) => {
  if (val.editStatus === editStatus.EDIT){
    showBody.value = val.showBody
    headId.value = val.editData.id
    props.editConfig.editStatus = val.editStatus
    props.editConfig.editData = val.editData
    // 确保保留autoCreated和hasSaved标记
    if (val.editData.autoCreated !== undefined) {
      props.editConfig.autoCreated = val.editData.autoCreated;
    }
    if (val.editData.hasSaved !== undefined) {
      props.editConfig.hasSaved = val.editData.hasSaved;
    }
  } else {
    // 如果是关闭标签页操作，需要传递正确的标记
    if (val) {
      // 确保传递当前的标记状态
      const backData = {
        ...val,
        autoCreated: props.editConfig.autoCreated,
        hasSaved: props.editConfig.hasSaved
      };
      emit('onEditBack', backData);
    }
  }
}


/* 初始化操作 */
onMounted(()=>{
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    headId.value = ''
    showBody.value = false
  } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    headId.value = props.editConfig.editData.id
    showBody.value = true;
  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    headId.value = props.editConfig.editData.id
    showBody.value = true
  }
})



/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true
})

</script>

<style lang="less" scoped>

</style>
